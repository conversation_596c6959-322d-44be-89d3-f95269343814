# pythonw runs program in background - pythonw notification.py

from plyer import notification
import time
import os
import warnings

# Suppress threading warnings from plyer
warnings.filterwarnings("ignore")

if __name__ == '__main__':
    # Constants
    TITLE = " *** Drink Water *** "
    MESSAGE = "Getting enough water every day is important for your health. Drinking water can prevent dehydration, a condition that can cause unclear thinking, result in mood change, cause your body to overheat, and lead to constipation and kidney stones."
    TIMEOUT = 5
    SLEEP_DURATION = 3600  # 1 hour

    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    icon_path = os.path.join(script_dir, "water.ico")

    # Check if icon file exists
    if os.path.exists(icon_path):
        print(f"Icon file found at: {icon_path}")
    else:
        print(f"Icon file not found at: {icon_path}")
        icon_path = None

    while True:
        try:
            # Send notification with icon (background errors are normal on Windows)
            notification.notify(
                title=TITLE,
                message=MESSAGE,
                app_icon=icon_path,
                timeout=TIMEOUT)
            print("Water reminder notification sent with icon")
        except Exception as e:
            # Fallback: send notification without icon
            try:
                notification.notify(
                    title=TITLE,
                    message=MESSAGE,
                    timeout=TIMEOUT)
                print("Water reminder notification sent without icon")
            except Exception as e2:
                print(f"Failed to send notification: {e2}")

        time.sleep(SLEEP_DURATION)