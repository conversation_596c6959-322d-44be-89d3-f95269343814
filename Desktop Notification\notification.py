# pythonw runs program in background - pythonw notification.py

from plyer import notification
import time
import os

if __name__ == '__main__':
    # Constants
    TITLE = " *** Drink Water *** "
    MESSAGE = "Getting enough water every day is important for your health. Drinking water can prevent dehydration, a condition that can cause unclear thinking, result in mood change, cause your body to overheat, and lead to constipation and kidney stones."
    TIMEOUT = 5
    SLEEP_DURATION = 3600  # 1 hour

    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    icon_path = os.path.join(script_dir, "water.ico")

    while True:
        try:
            # Try with icon first
            if os.path.exists(icon_path):
                notification.notify(
                    title=TITLE,
                    message=MESSAGE,
                    app_icon=icon_path,
                    timeout=TIMEOUT)
                print("Notification sent with icon")
            else:
                # Fallback without icon
                notification.notify(
                    title=TITLE,
                    message=MESSAGE,
                    timeout=TIMEOUT)
                print("Notification sent without icon (icon file not found)")
        except Exception as e:
            print(f"Error with icon: {e}")
            # Try without icon as fallback
            try:
                notification.notify(
                    title=TITLE,
                    message=MESSAGE,
                    timeout=TIMEOUT)
                print("Notification sent without icon (fallback)")
            except Exception as e2:
                print(f"Failed to send notification: {e2}")

        time.sleep(SLEEP_DURATION)