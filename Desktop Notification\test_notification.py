# Test script to send a single notification

from plyer import notification

if __name__ == '__main__':
    # Constants
    TITLE = " *** Drink Water *** "
    MESSAGE = "Getting enough water every day is important for your health. Drinking water can prevent dehydration, a condition that can cause unclear thinking, result in mood change, cause your body to overheat, and lead to constipation and kidney stones."
    TIMEOUT = 5
    
    try:
        notification.notify(
            title=TITLE,
            message=MESSAGE,
            timeout=TIMEOUT)
        print("Test notification sent successfully!")
    except Exception as e:
        print(f"Failed to send notification: {e}")
